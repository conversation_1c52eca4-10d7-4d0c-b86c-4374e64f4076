/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#2DD4BF', // Teal-400: Bright teal
          DEFAULT: '#0D9488', // Teal-600: Deep teal
          dark: '#134E4A', // Teal-900: Dark teal
        },
        accent: {
          light: '#F472B6', // Pink-400: Soft pink
          DEFAULT: '#DB2777', // Pink-600: Rich pink
          dark: '#9D174D', // Pink-800: Deep pink
        },
        neutral: {
          50: '#F8FAFC',
          100: '#F1F5F9',
          600: '#475569',
          800: '#1E293B',
        },
        indigo: {
          400: '#818CF8',
          500: '#6366F1',
          600: '#4F46E5',
          700: '#4338CA',
        },
        sky: {
          400: '#38BDF8',
          500: '#0EA5E9',
          600: '#0284C7',
          700: '#0369A1',
        },
        blue: {
          500: '#3B82F6',
          600: '#2563EB',
          700: '#1D4ED8',
        },
        slate: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1E293B',
          900: '#0f172a',
        },
        white: '#ffffff',
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [],
  safelist: [
    // Text colors
    'text-white',
    'text-slate-600',
    'text-slate-800',
    // Background colors
    'bg-white',
    // Border colors
    'border-slate-100',
    // Font weights
    'font-medium',
    'font-semibold',
    'font-bold',
    // Padding
    'py-3',
    'px-8',
    'p-6',
    // Margins
    'mt-4',
    // Rounded
    'rounded-lg',
    'rounded-xl',
    // Shadows
    'shadow-sm',
    'shadow-md',
    'hover:shadow-lg',
    // Transitions
    'transition-all',
    'duration-200',
    'duration-300',
    // Responsive
    'sm:text-4xl',
    // Other
    'border',
  ],
};