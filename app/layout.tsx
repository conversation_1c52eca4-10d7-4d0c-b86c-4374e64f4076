import './globals.css';
import '@fortawesome/fontawesome-free/css/all.css';
import 'aos/dist/aos.css';
import { Inter } from 'next/font/google';
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import Navigation from './components/Navigation';
import AosInitializer from './components/AosInitializer';

const inter = Inter({ subsets: ['latin'] });

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Difinity.ai - Privacy-First AI Infrastructure",
  description: "Deploy, monitor, and control AI workloads with complete data sovereignty and cost transparency. Infrastructure-agnostic, privacy-first AI platform for enterprise.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Basic SEO */}
        <meta
          name="description"
          content="Difinity.ai provides secure, ethical, and scalable AI infrastructure for building, deploying, and managing AI applications at scale."
        />
        <meta name="keywords" content="AI, AI Infrastructure, Enterprise AI, Difinity.ai, AI Solutions" />
        <meta name="author" content="Difinity.ai" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />

        {/* Open Graph (OG) Tags for Social Media */}
        <meta property="og:title" content="Difinity.ai - Enterprise AI Gateway & Compliance Platform" />
        <meta
          property="og:description"
          content="Building the future of AI infrastructure with secure, ethical, and scalable solutions."
        />
        {/**
         * FIX LOGO REPLAEMENTS
         */}
        <meta property="og:image" content="/og-image.jpg" />
        <meta property="og:url" content="https://difinity.ai" />
        <meta property="og:type" content="website" />

        {/* Twitter Card Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Difinity.ai - Enterprise AI Gateway & Compliance Platform" />
        <meta
          name="twitter:description"
          content="Building the future of AI infrastructure with secure, ethical, and scalable solutions."
        />
        {/* Replace with your actual image path */}
        <meta name="twitter:image" content="/og-image.jpg" />
        {/* Replace with your Twitter handle */}
        <meta name="twitter:site" content="@difinity_ai" />

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />

        {/* Font Awesome */}
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} ${inter.className}`}>
        <AosInitializer />
        <Navigation />
        {children}
      </body>
    </html>
  );
}