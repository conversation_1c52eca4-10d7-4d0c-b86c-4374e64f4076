@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 15, 23, 42; /* slate-900 */
  --background-rgb: 255, 255, 255;
  /* Holistic AI Inspired Theme - Clean & Professional */
  --primary-gradient: linear-gradient(135deg, #1a1f36 0%, #2d3748 50%, #4a5568 100%);
  --secondary-gradient: linear-gradient(135deg, #f7fafc 0%, #edf2f7 50%, #e2e8f0 100%);
  --accent-gradient: linear-gradient(135deg, #6b46c1 0%, #8b5cf6 50%, #a78bfa 100%);
  --blue-gradient: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #93c5fd 100%);
  --neutral-gradient: linear-gradient(135deg, #f7fafc 0%, #edf2f7 50%, #e2e8f0 100%);
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Parallax scrolling effects */
.parallax-container {
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  perspective: 1px;
}

.parallax-element {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.parallax-back {
  transform: translateZ(-1px) scale(2);
}

.parallax-base {
  transform: translateZ(0);
}

/* Section transitions */
.section-transition {
  position: relative;
  z-index: 10;
  background: linear-gradient(to bottom, transparent 0%, white 10%, white 90%, transparent 100%);
}

/* Ensure backdrop-filter support */
@supports (backdrop-filter: blur(10px)) {
  .card,
  .card-feature,
  .form-input,
  .stat-card {
    backdrop-filter: blur(8px);
  }
}

@layer components {
  /* Clean, professional buttons */
  .btn-primary {
    @apply bg-primary text-white px-6 py-3 rounded-lg font-medium text-base
           hover:bg-primary-light hover:shadow-lg transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-white text-primary border-2 border-primary
           px-6 py-3 rounded-lg font-medium text-base
           hover:bg-primary hover:text-white hover:shadow-lg
           transition-all duration-200;
  }

  .btn-accent {
    @apply bg-accent text-white px-6 py-3 rounded-lg font-medium text-base
           hover:bg-accent-dark hover:shadow-lg transition-all duration-200;
  }

  .btn-blue {
    @apply bg-blue text-white px-6 py-3 rounded-lg font-medium text-base
           hover:bg-blue-dark hover:shadow-lg transition-all duration-200;
  }

  .btn-large {
    @apply px-8 py-4 text-lg;
  }

  /* Navigation links */
  .nav-link {
    @apply text-gray-600 hover:text-primary font-medium
           transition-all duration-200;
  }

  /* Clean, professional cards */
  .card {
    @apply bg-white p-6 rounded-xl shadow-sm border border-gray-100
           hover:shadow-md transition-all duration-200;
  }

  .card-feature {
    @apply bg-white p-8 rounded-xl shadow-sm border border-gray-100
           hover:shadow-lg hover:-translate-y-1 transition-all duration-300;
  }

  .card-large {
    @apply p-12;
  }

  /* Clean, professional headings */
  .section-heading {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-primary;
  }

  .section-subheading {
    @apply text-xl md:text-2xl text-gray-600 font-normal leading-relaxed;
  }

  .heading-large {
    @apply text-5xl md:text-6xl lg:text-7xl;
  }

  .heading-medium {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  .heading-small {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  /* Container width for consistent layout */
  .container-width {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Gradient text */
  .gradient-text {
    background: var(--accent-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .gradient-text-blue {
    background: var(--blue-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  /* Clean form inputs */
  .form-input {
    @apply w-full px-4 py-3 rounded-lg border border-gray-300
           focus:border-accent focus:ring-2 focus:ring-accent/20
           transition-all duration-200 bg-white;
  }

  .form-label {
    @apply text-gray-700 font-medium text-sm;
  }

  .form-group {
    @apply space-y-2;
  }

  /* Icon containers */
  .icon-container {
    @apply w-12 h-12 rounded-lg flex items-center justify-center
           bg-secondary text-primary transition-all duration-200;
  }

  .icon-large {
    @apply w-16 h-16;
  }

  /* Stats cards */
  .stat-card {
    @apply bg-white p-4 rounded-lg border border-gray-100 text-center
           hover:shadow-md transition-all duration-200;
  }

  /* Feature highlight */
  .feature-highlight {
    @apply border border-gray-200 rounded-xl p-6 transition-all duration-200
           hover:border-accent/30 hover:shadow-md;
  }
}

/* Clean background pattern */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(26, 31, 54, 0.02) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(107, 70, 193, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Subtle animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Pulse glow animation */
@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(13, 148, 136, 0.3); }
  50% { box-shadow: 0 0 40px rgba(6, 182, 212, 0.5); }
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

/* Gradient animation */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* Slide in animations */
.slide-in-left {
  transform: translateX(-100px);
  opacity: 0;
  transition: all 0.8s ease-out;
}

.slide-in-right {
  transform: translateX(100px);
  opacity: 0;
  transition: all 0.8s ease-out;
}

.slide-in-up {
  transform: translateY(50px);
  opacity: 0;
  transition: all 0.8s ease-out;
}

.slide-in-left.animate,
.slide-in-right.animate,
.slide-in-up.animate {
  transform: translate(0);
  opacity: 1;
}

/* Section animations */
section {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

section.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Parallax sections */
.parallax-section {
  will-change: transform;
}

/* Smooth page transitions */
.page-transition {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}