export default function ContactSection() {
  return (
    <div className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50/30"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

      <div className="container-width relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-teal-100 to-orange-100 border border-teal-200/50 mb-6">
            <span className="text-sm font-semibold gradient-text">💬 Get in Touch</span>
          </div>
          <h2 className="section-heading mb-6">
            Ready to <span className="gradient-text-accent">Transform</span> Your AI Infrastructure?
          </h2>
          <p className="section-subheading max-w-3xl mx-auto">
            Let's discuss how Difinity.ai can help you achieve complete control, privacy, and cost transparency for your AI workloads.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <div className="card-feature" data-aos="fade-right">
            <h3 className="text-2xl font-bold text-slate-800 mb-6">Send us a message</h3>
            <form className="space-y-6">
              <div className="form-group">
                <label className="form-label">Full Name</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="Enter your full name"
                />
              </div>

              <div className="form-group">
                <label className="form-label">Email Address</label>
                <input
                  type="email"
                  className="form-input"
                  placeholder="Enter your email address"
                />
              </div>

              <div className="form-group">
                <label className="form-label">Company</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="Enter your company name"
                />
              </div>

              <div className="form-group">
                <label className="form-label">Message</label>
                <textarea
                  rows={4}
                  className="form-input resize-none"
                  placeholder="Tell us about your AI infrastructure needs..."
                ></textarea>
              </div>

              <button type="submit" className="btn-primary w-full">
                <span>Send Message</span>
                <i className="fas fa-paper-plane ml-2"></i>
              </button>
            </form>
          </div>

          {/* Contact Information */}
          <div className="space-y-8" data-aos="fade-left">
            <div className="feature-highlight">
              <h3 className="text-2xl font-bold text-slate-800 mb-6">Get Started Today</h3>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="icon-container mr-4 flex-shrink-0">
                    <i className="fas fa-rocket text-teal-600"></i>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-slate-800 mb-2">Free Trial</h4>
                    <p className="text-slate-600">Start with our free tier and experience the power of privacy-first AI infrastructure.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="icon-container mr-4 flex-shrink-0">
                    <i className="fas fa-calendar text-orange-600"></i>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-slate-800 mb-2">Schedule Demo</h4>
                    <p className="text-slate-600">Book a personalized demo to see how Difinity.ai fits your specific needs.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="icon-container mr-4 flex-shrink-0">
                    <i className="fas fa-headset text-cyan-600"></i>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-slate-800 mb-2">Expert Support</h4>
                    <p className="text-slate-600">Our team of AI infrastructure experts is here to help you succeed.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="card-feature text-center">
              <h4 className="text-xl font-bold text-slate-800 mb-4">Contact Information</h4>
              <div className="space-y-4">
                <div className="flex items-center justify-center">
                  <i className="fas fa-envelope text-teal-500 mr-3"></i>
                  <span className="text-slate-600"><EMAIL></span>
                </div>
                <div className="flex items-center justify-center">
                  <i className="fas fa-map-marker-alt text-orange-500 mr-3"></i>
                  <span className="text-slate-600">Sydney, NSW</span>
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-4">
              <button className="btn-primary w-full">
                <span>Schedule Demo</span>
                <i className="fas fa-calendar ml-2"></i>
              </button>
              <button className="btn-secondary w-full">
                <span>Start Free Trial</span>
                <i className="fas fa-arrow-right ml-2"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}