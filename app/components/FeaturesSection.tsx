export default function FeaturesSection() {
  const mainFeatures = [
    {
      title: "Enterprise Security",
      description: "SOC 2 compliant infrastructure with end-to-end encryption, data sovereignty, and zero-trust architecture.",
      icon: "🛡️",
      color: "accent"
    },
    {
      title: "Governance & Control",
      description: "Centralized governance with role-based access, audit trails, and compliance reporting for enterprise oversight.",
      icon: "⚙️",
      color: "primary"
    },
    {
      title: "Cost Optimization",
      description: "Transparent 10% markup pricing with real-time cost analytics, budget controls, and ROI tracking.",
      icon: "📊",
      color: "success"
    },
    {
      title: "Infrastructure Agnostic",
      description: "Deploy on any infrastructure - on-premises, private cloud, or multi-cloud. No vendor lock-in.",
      icon: "☁️",
      color: "accent"
    },
    {
      title: "Performance Monitoring",
      description: "Enterprise-grade monitoring with SLA tracking, performance analytics, and proactive alerting.",
      icon: "📈",
      color: "primary"
    },
    {
      title: "Financial Intelligence",
      description: "Detailed cost allocation, chargeback reporting, and budget forecasting for financial transparency.",
      icon: "💰",
      color: "success"
    }
  ];

  const deploymentOptions = [
    {
      title: "On-Premises",
      description: "Maximum security with air-gapped deployment",
      icon: "🏢",
      features: ["Complete data isolation", "Custom security policies", "Regulatory compliance"],
      highlight: "Most Secure"
    },
    {
      title: "Private Cloud",
      description: "Deploy in your dedicated cloud environment",
      icon: "☁️",
      features: ["Your cloud infrastructure", "Data residency control", "Enterprise SLAs"],
      highlight: "Most Popular"
    },
    {
      title: "Hybrid & Multi-Cloud",
      description: "Flexible deployment across environments",
      icon: "🌐",
      features: ["Workload distribution", "Disaster recovery", "Cost optimization"],
      highlight: "Most Flexible"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container-width">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-accent/10 border border-accent/20 mb-6">
            <span className="text-sm font-medium text-accent">
              🏢 Enterprise Platform Features
            </span>
          </div>
          <h2 className="heading-medium font-bold text-primary mb-6">
            Security, Governance & <span className="gradient-text">Financial Control</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Enterprise-grade AI infrastructure platform with comprehensive security, governance, and cost management capabilities.
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-24">
          {mainFeatures.map((feature, index) => (
            <div
              key={index}
              className="group relative bg-white rounded-2xl p-8 border border-gray-100 hover:border-accent/30 hover:shadow-xl transition-all duration-300"
            >
              {/* Icon */}
              <div className={`w-16 h-16 rounded-xl flex items-center justify-center text-2xl mb-6 ${
                feature.color === 'accent' ? 'bg-accent/10' :
                feature.color === 'success' ? 'bg-success/10' : 'bg-primary/10'
              }`}>
                {feature.icon}
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold mb-4 text-primary group-hover:text-accent transition-colors">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>

              {/* Hover Effect */}
              <div className={`absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${
                feature.color === 'accent' ? 'bg-accent/5' :
                feature.color === 'success' ? 'bg-success/5' : 'bg-primary/5'
              }`}></div>
            </div>
          ))}
        </div>

        {/* Deployment Options */}
        <div className="mb-24">
          <div className="text-center mb-16">
            <h3 className="heading-medium font-bold text-primary mb-4">
              Enterprise Deployment Models
            </h3>
            <p className="text-xl text-gray-600">
              Choose the deployment model that meets your security and compliance requirements
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {deploymentOptions.map((option, index) => (
              <div
                key={index}
                className="relative bg-white rounded-2xl p-8 border-2 border-gray-100 hover:border-accent/50 transition-all duration-300 group"
              >
                {/* Highlight Badge */}
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-accent text-white px-4 py-1 rounded-full text-sm font-medium">
                    {option.highlight}
                  </span>
                </div>

                {/* Icon */}
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto bg-accent/10 rounded-2xl flex items-center justify-center text-3xl mb-4">
                    {option.icon}
                  </div>
                  <h4 className="text-xl font-bold text-primary mb-2">{option.title}</h4>
                  <p className="text-gray-600">{option.description}</p>
                </div>

                {/* Features */}
                <ul className="space-y-3">
                  {option.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-600">
                      <div className="w-5 h-5 bg-success/20 rounded-full flex items-center justify-center mr-3">
                        <svg className="w-3 h-3 text-success" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-br from-accent/5 to-primary/5 rounded-3xl p-12 border border-accent/20">
            <h3 className="heading-small font-bold text-primary mb-4">
              Ready for Enterprise AI?
            </h3>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Experience enterprise-grade AI infrastructure with complete security, governance, and cost transparency.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary btn-large">
                Schedule Enterprise Demo
              </button>
              <button className="btn-secondary btn-large">
                Contact Sales
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}