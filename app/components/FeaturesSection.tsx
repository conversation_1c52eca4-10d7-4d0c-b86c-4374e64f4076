export default function FeaturesSection() {
  const mainFeatures = [
    {
      title: "Enterprise Security",
      description: "SOC 2 compliant infrastructure with end-to-end encryption, data sovereignty, and zero-trust architecture.",
      icon: "shield-alt",
      gradient: "from-slate-600 to-slate-800"
    },
    {
      title: "Governance & Control",
      description: "Centralized governance with role-based access, audit trails, and compliance reporting for enterprise oversight.",
      icon: "cogs",
      gradient: "from-amber-500 to-amber-700"
    },
    {
      title: "Cost Optimization",
      description: "Transparent 10% markup pricing with real-time cost analytics, budget controls, and ROI tracking.",
      icon: "chart-line",
      gradient: "from-blue-600 to-blue-800"
    },
    {
      title: "Infrastructure Agnostic",
      description: "Deploy on any infrastructure - on-premises, private cloud, or multi-cloud. No vendor lock-in.",
      icon: "cloud",
      gradient: "from-green-600 to-green-800"
    },
    {
      title: "Performance Monitoring",
      description: "Enterprise-grade monitoring with SLA tracking, performance analytics, and proactive alerting.",
      icon: "eye",
      gradient: "from-purple-600 to-purple-800"
    },
    {
      title: "Financial Intelligence",
      description: "Detailed cost allocation, chargeback reporting, and budget forecasting for financial transparency.",
      icon: "dollar-sign",
      gradient: "from-orange-600 to-orange-800"
    }
  ];

  const deploymentOptions = [
    {
      title: "On-Premises",
      description: "Maximum security with air-gapped deployment",
      icon: "server",
      features: ["Complete data isolation", "Custom security policies", "Regulatory compliance"]
    },
    {
      title: "Private Cloud",
      description: "Deploy in your dedicated cloud environment",
      icon: "cloud-upload-alt",
      features: ["Your cloud infrastructure", "Data residency control", "Enterprise SLAs"]
    },
    {
      title: "Hybrid & Multi-Cloud",
      description: "Flexible deployment across environments",
      icon: "globe",
      features: ["Workload distribution", "Disaster recovery", "Cost optimization"]
    }
  ];

  return (
    <div className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/30"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

      <div className="container-width relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-slate-100 to-amber-50 border border-slate-200/50 mb-6">
            <span className="text-sm font-semibold bg-gradient-to-r from-slate-700 to-amber-600 bg-clip-text text-transparent">🏢 Enterprise Platform Features</span>
          </div>
          <h2 className="section-heading mb-6">
            Security, Governance & <span className="gradient-text-accent">Financial Control</span>
          </h2>
          <p className="section-subheading max-w-3xl mx-auto">
            Enterprise-grade AI infrastructure platform with comprehensive security, governance, and cost management capabilities.
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-24">
          {mainFeatures.map((feature, index) => (
            <div
              key={index}
              className="card-feature group"
              data-aos="fade-up"
              data-aos-delay={index * 100}
            >
              <div className="icon-container mb-6">
                <i className={`fas fa-${feature.icon} text-2xl bg-gradient-to-r ${feature.gradient} bg-clip-text text-transparent`}></i>
              </div>
              <h3 className="text-xl font-bold mb-4 text-slate-800 group-hover:text-slate-900 transition-colors">
                {feature.title}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Deployment Options */}
        <div className="mb-24">
          <div className="text-center mb-16" data-aos="fade-up">
            <h3 className="text-3xl md:text-4xl font-bold mb-4 gradient-text-accent">Enterprise Deployment Models</h3>
            <p className="text-xl text-slate-600">Choose the deployment model that meets your security and compliance requirements</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {deploymentOptions.map((option, index) => (
              <div
                key={index}
                className="feature-highlight group text-center"
                data-aos="fade-up"
                data-aos-delay={index * 150}
              >
                <div className="icon-container mx-auto mb-6">
                  <i className={`fas fa-${option.icon} text-2xl text-slate-600`}></i>
                </div>
                <h4 className="text-xl font-bold mb-3 text-slate-800">{option.title}</h4>
                <p className="text-slate-600 mb-6">{option.description}</p>
                <ul className="space-y-2">
                  {option.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center justify-center text-sm text-slate-600">
                      <i className="fas fa-check text-amber-500 mr-2"></i>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center" data-aos="fade-up">
          <div className="rounded-3xl p-12 border border-slate-200" style={{background: 'linear-gradient(to right, rgba(30, 41, 59, 0.05), rgba(245, 158, 11, 0.05))'}}>
            <h3 className="text-3xl font-bold mb-4 gradient-text-accent">Ready for Enterprise AI?</h3>
            <p className="text-xl text-slate-600 mb-8 max-w-2xl mx-auto">
              Experience enterprise-grade AI infrastructure with complete security, governance, and cost transparency.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                <span>Schedule Enterprise Demo</span>
              </button>
              <button className="btn-accent">
                <span>Contact Sales</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}