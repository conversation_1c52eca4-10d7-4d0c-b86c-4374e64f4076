export default function CoreComponents() {
  const components = [
    {
      name: "Difinity Hub",
      subtitle: "Central Management Console",
      description: "Complete control over AI workloads and governance policies for organization administrators.",
      features: [
        "User Management & Role-Based Access",
        "API Key Management & Security",
        "Model Configuration & Routing",
        "Content Moderation Configuration",
        "Compliance Management Framework"
      ],
      icon: "🎛️",
      color: "accent"
    },
    {
      name: "Difinity Flow",
      subtitle: "Unified AI Processing Engine",
      description: "Core processing engine providing unified REST API for all AI workloads with complete compliance.",
      features: [
        "Unified API for 50+ AI Models",
        "Dynamic Model Selection",
        "Real-time Content Moderation",
        "Automated Compliance Verification",
        "Responsible AI Filtering"
      ],
      icon: "⚡",
      color: "primary"
    },
    {
      name: "Difinity Echo",
      subtitle: "Monitoring & Analytics",
      description: "Comprehensive monitoring, logging, and analytics for complete visibility into AI usage and costs.",
      features: [
        "Real-time Usage Monitoring",
        "Cost Analysis & Optimization",
        "Compliance Monitoring",
        "Performance Analytics",
        "ROI Analysis & Reporting"
      ],
      icon: "📊",
      color: "success"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container-width">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-accent/10 border border-accent/20 mb-6">
            <span className="text-sm font-medium text-accent">
              🏗️ Platform Architecture
            </span>
          </div>
          <h2 className="heading-medium font-bold text-primary mb-6">
            Three Core Components
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Difinity AI consists of three integrated components that work together to provide 
            complete AI governance, processing, and monitoring capabilities.
          </p>
        </div>

        {/* Components */}
        <div className="space-y-24">
          {components.map((component, index) => (
            <div
              key={index}
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Content */}
              <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                <div className="flex items-center mb-6">
                  <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mr-4 ${
                    component.color === 'accent' ? 'bg-accent/10' :
                    component.color === 'success' ? 'bg-success/10' : 'bg-primary/10'
                  }`}>
                    <span className="text-3xl">{component.icon}</span>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-primary">{component.name}</h3>
                    <p className={`text-sm font-medium ${
                      component.color === 'accent' ? 'text-accent' :
                      component.color === 'success' ? 'text-success' : 'text-primary'
                    }`}>
                      {component.subtitle}
                    </p>
                  </div>
                </div>
                
                <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                  {component.description}
                </p>

                <div className="space-y-4">
                  <h4 className="font-semibold text-primary mb-4">Key Features:</h4>
                  {component.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <div className={`w-5 h-5 rounded-full flex items-center justify-center mr-3 ${
                        component.color === 'accent' ? 'bg-accent/20' :
                        component.color === 'success' ? 'bg-success/20' : 'bg-primary/20'
                      }`}>
                        <svg className={`w-3 h-3 ${
                          component.color === 'accent' ? 'text-accent' :
                          component.color === 'success' ? 'text-success' : 'text-primary'
                        }`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Visual */}
              <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                <div className={`rounded-2xl p-8 border-2 ${
                  component.color === 'accent' ? 'bg-accent/5 border-accent/20' :
                  component.color === 'success' ? 'bg-success/5 border-success/20' : 'bg-primary/5 border-primary/20'
                }`}>
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                    {/* Mock Interface */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${
                          component.color === 'accent' ? 'bg-accent' :
                          component.color === 'success' ? 'bg-success' : 'bg-primary'
                        }`}></div>
                        <div className="h-3 bg-gray-200 rounded w-20"></div>
                      </div>
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className={`h-16 rounded-lg ${
                            component.color === 'accent' ? 'bg-accent/10' :
                            component.color === 'success' ? 'bg-success/10' : 'bg-primary/10'
                          }`}></div>
                        ))}
                      </div>
                      
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Integration CTA */}
        <div className="text-center mt-20">
          <div className="bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl p-12 border border-gray-200">
            <h3 className="text-2xl font-bold text-primary mb-4">
              Seamlessly Integrated Platform
            </h3>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              All three components work together to provide a unified AI governance experience 
              with complete data sovereignty and enterprise-grade security.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary btn-large">
                Explore Platform
              </button>
              <button className="btn-secondary btn-large">
                Technical Documentation
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
