export default function TrustSection() {
  const trustedCompanies = [
    { name: "Microsoft", logo: "🏢" },
    { name: "Google", logo: "🔍" },
    { name: "Amazon", logo: "📦" },
    { name: "IBM", logo: "💼" },
    { name: "Oracle", logo: "🗄️" },
    { name: "Salesforce", logo: "☁️" },
    { name: "Adobe", logo: "🎨" },
    { name: "Nvidia", logo: "🖥️" },
  ];

  return (
    <section className="py-16 bg-secondary-light border-y border-gray-200">
      <div className="container-width">
        <div className="text-center mb-12">
          <h2 className="text-2xl font-bold text-primary mb-4">
            Trusted by World Leading Brands
          </h2>
        </div>
        
        {/* Logo Carousel */}
        <div className="relative overflow-hidden">
          <div className="flex animate-scroll">
            {/* First set */}
            {trustedCompanies.map((company, index) => (
              <div
                key={index}
                className="flex-shrink-0 mx-8 flex items-center justify-center"
              >
                <div className="flex items-center space-x-3 text-gray-500 hover:text-primary transition-colors duration-200">
                  <span className="text-2xl">{company.logo}</span>
                  <span className="font-medium text-lg">{company.name}</span>
                </div>
              </div>
            ))}
            {/* Duplicate for seamless loop */}
            {trustedCompanies.map((company, index) => (
              <div
                key={`duplicate-${index}`}
                className="flex-shrink-0 mx-8 flex items-center justify-center"
              >
                <div className="flex items-center space-x-3 text-gray-500 hover:text-primary transition-colors duration-200">
                  <span className="text-2xl">{company.logo}</span>
                  <span className="font-medium text-lg">{company.name}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <style jsx>{`
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        
        .animate-scroll {
          animation: scroll 30s linear infinite;
        }
      `}</style>
    </section>
  );
}
