'use client';

import { useEffect, useRef } from 'react';

interface ParallaxSectionProps {
  children: React.ReactNode;
  speed?: number;
  className?: string;
  id?: string;
}

export default function ParallaxSection({ 
  children, 
  speed = 0.5, 
  className = '',
  id 
}: ParallaxSectionProps) {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const scrolled = window.pageYOffset;
      const section = sectionRef.current;
      const rect = section.getBoundingClientRect();
      const sectionTop = scrolled + rect.top;
      const sectionHeight = rect.height;
      const windowHeight = window.innerHeight;

      // Only apply parallax when section is in viewport
      if (scrolled + windowHeight > sectionTop && scrolled < sectionTop + sectionHeight) {
        const yPos = -(scrolled - sectionTop) * speed;
        section.style.transform = `translateY(${yPos}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return (
    <section 
      ref={sectionRef}
      id={id}
      className={`relative transition-transform duration-75 ease-out ${className}`}
    >
      {children}
    </section>
  );
}
