export default function PricingSection() {
  const pricingOptions = [
    {
      name: "Cloud Hosted",
      price: "10%",
      period: "markup on AI usage",
      description: "Fully managed cloud solution with zero infrastructure overhead",
      features: [
        "Fully managed infrastructure",
        "Automatic scaling & updates",
        "99.9% uptime SLA",
        "Global edge deployment",
        "Real-time cost monitoring",
        "Advanced analytics dashboard",
        "24/7 support included",
        "Pay only for what you use"
      ],
      cta: "Start Free Trial",
      popular: true,
      type: "cloud",
      icon: "cloud",
      color: "teal"
    },
    {
      name: "Private Cloud & On-Premises",
      price: "Custom",
      period: "enterprise pricing",
      description: "Complete control with your own infrastructure deployment",
      features: [
        "Deploy in your own cloud",
        "On-premises installation",
        "Complete data sovereignty",
        "Custom security policies",
        "Dedicated support team",
        "Professional services included",
        "Custom integrations",
        "Training & onboarding"
      ],
      cta: "Contact Sales",
      popular: false,
      type: "private",
      icon: "shield-alt",
      color: "orange"
    }
  ];

  const comparisons = [
    {
      title: "Traditional Integration",
      points: [
        "Multiple API integrations",
        "Manual compliance checks",
        "Basic monitoring",
        "Fixed routing",
        "Limited support",
      ],
      isNegative: true
    },
    {
      title: "With Difinity.ai",
      points: [
        "Single API integration",
        "Automated compliance",
        "Advanced monitoring",
        "Smart routing",
        "24/7 support",
      ],
      isNegative: false
    }
  ];

  return (
    <div className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-amber-50/30"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>

      <div className="container-width relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-slate-100 to-amber-50 border border-slate-200/50 mb-6">
            <span className="text-sm font-semibold bg-gradient-to-r from-slate-700 to-amber-600 bg-clip-text text-transparent">💼 Enterprise Pricing</span>
          </div>
          <h2 className="section-heading mb-6">
            Transparent, Enterprise <span className="gradient-text-accent">Pricing</span>
          </h2>
          <p className="section-subheading max-w-3xl mx-auto">
            Predictable pricing with complete cost transparency. Choose the deployment model that fits your enterprise requirements.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-2 gap-12 mb-20 max-w-6xl mx-auto">
          {pricingOptions.map((option, index) => (
            <div
              key={index}
              className={`relative ${option.popular ? 'lg:scale-105' : ''}`}
              data-aos="fade-up"
              data-aos-delay={index * 200}
            >
              {option.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="text-white px-6 py-2 rounded-full text-sm font-semibold" style={{background: 'var(--primary-gradient)'}}>
                    Most Popular
                  </div>
                </div>
              )}

              <div className={`card-feature h-full group ${option.popular ? 'border-slate-300 shadow-2xl' : 'border-amber-200'}`}>
                {/* Icon Header */}
                <div className="text-center mb-8">
                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center ${option.color === 'teal' ? 'bg-gradient-to-br from-slate-100 to-slate-200' : 'bg-gradient-to-br from-amber-100 to-amber-200'}`}>
                    <i className={`fas fa-${option.icon} text-3xl ${option.color === 'teal' ? 'text-slate-600' : 'text-amber-600'}`}></i>
                  </div>
                  <h3 className="text-3xl font-bold text-slate-800 mb-3">{option.name}</h3>
                  <p className="text-slate-600 mb-6 text-lg">{option.description}</p>

                  <div className="mb-8">
                    <span className={`text-6xl font-bold ${option.color === 'teal' ? 'gradient-text' : 'gradient-text-accent'}`}>
                      {option.price}
                    </span>
                    <div className="text-slate-500 mt-2 text-lg">{option.period}</div>
                  </div>

                  <button className={`w-full ${option.popular ? 'btn-primary' : 'btn-accent'}`}>
                    <span>{option.cta}</span>
                  </button>
                </div>

                <div className="space-y-4">
                  {option.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center">
                      <i className={`fas fa-check mr-4 ${option.color === 'teal' ? 'text-slate-500' : 'text-amber-500'}`}></i>
                      <span className="text-slate-600 text-lg">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Value Proposition */}
        <div className="text-center" data-aos="fade-up">
          <div className="rounded-3xl p-12 border border-slate-200" style={{background: 'linear-gradient(to right, rgba(30, 41, 59, 0.05), rgba(245, 158, 11, 0.05))'}}>
            <h3 className="text-3xl font-bold mb-6 gradient-text">Enterprise AI Infrastructure Advantage</h3>

            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <div className="text-left">
                <h4 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                  <i className="fas fa-times text-red-500 mr-3"></i>
                  Traditional AI Platforms
                </h4>
                <ul className="space-y-3 text-slate-600">
                  <li>• Data leaves your infrastructure</li>
                  <li>• Hidden costs and surprise bills</li>
                  <li>• Vendor lock-in and dependencies</li>
                  <li>• Complex, unpredictable pricing</li>
                  <li>• Limited compliance controls</li>
                </ul>
              </div>

              <div className="text-left">
                <h4 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                  <i className="fas fa-check text-amber-500 mr-3"></i>
                  Difinity.ai Enterprise Platform
                </h4>
                <ul className="space-y-3 text-slate-600">
                  <li>• Complete data sovereignty & control</li>
                  <li>• Transparent 10% markup pricing</li>
                  <li>• Infrastructure agnostic deployment</li>
                  <li>• Real-time cost intelligence & ROI</li>
                  <li>• Enterprise security & compliance</li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                <span>Schedule Enterprise Demo</span>
              </button>
              <button className="btn-accent">
                <span>Contact Sales</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}