export default function PricingSection() {
  const pricingOptions = [
    {
      name: "Difinity Cloud",
      price: "Contact",
      period: "for pricing",
      description: "Fully managed AI governance platform in our secure cloud",
      features: [
        "Multi-tenant secure platform",
        "All three core components",
        "99.9% uptime SLA",
        "Global deployment",
        "Standard integrations",
        "24/7 support included",
        "Automatic updates",
        "Basic customization"
      ],
      cta: "Get Quote",
      popular: true,
      type: "cloud",
      icon: "cloud",
      color: "accent"
    },
    {
      name: "Private Deployment",
      price: "Custom",
      period: "enterprise licensing",
      description: "Complete AI governance platform deployed in your infrastructure",
      features: [
        "Your infrastructure deployment",
        "Complete data sovereignty",
        "Custom security policies",
        "Unlimited customization",
        "Dedicated support team",
        "Professional services",
        "Custom integrations",
        "Training & onboarding"
      ],
      cta: "Contact Sales",
      popular: false,
      type: "private",
      icon: "shield-alt",
      color: "primary"
    }
  ];

  const comparisons = [
    {
      title: "Traditional Integration",
      points: [
        "Multiple API integrations",
        "Manual compliance checks",
        "Basic monitoring",
        "Fixed routing",
        "Limited support",
      ],
      isNegative: true
    },
    {
      title: "With Difinity.ai",
      points: [
        "Single API integration",
        "Automated compliance",
        "Advanced monitoring",
        "Smart routing",
        "24/7 support",
      ],
      isNegative: false
    }
  ];

  return (
    <section className="py-24 bg-secondary-light">
      <div className="container-width">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-accent/10 border border-accent/20 mb-6">
            <span className="text-sm font-medium text-accent">
              💼 Enterprise Pricing
            </span>
          </div>
          <h2 className="heading-medium font-bold text-primary mb-6">
            Transparent, Enterprise <span className="gradient-text">Pricing</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Predictable pricing with complete cost transparency. Choose the deployment model that fits your enterprise requirements.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-2 gap-8 mb-20 max-w-6xl mx-auto">
          {pricingOptions.map((option, index) => (
            <div
              key={index}
              className={`relative ${option.popular ? 'lg:scale-105' : ''}`}
            >
              {option.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-accent text-white px-6 py-2 rounded-full text-sm font-medium">
                    Most Popular
                  </div>
                </div>
              )}

              <div className={`bg-white rounded-2xl p-8 h-full border-2 transition-all duration-300 ${
                option.popular
                  ? 'border-accent shadow-xl'
                  : 'border-gray-200 hover:border-accent/50 hover:shadow-lg'
              }`}>
                {/* Header */}
                <div className="text-center mb-8">
                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center ${
                    option.popular ? 'bg-accent/10' : 'bg-primary/10'
                  }`}>
                    <span className="text-3xl">
                      {option.icon === 'cloud' ? '☁️' : '🏢'}
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-3">{option.name}</h3>
                  <p className="text-gray-600 mb-6">{option.description}</p>

                  <div className="mb-8">
                    <span className={`text-5xl font-bold ${option.popular ? 'text-accent' : 'text-primary'}`}>
                      {option.price}
                    </span>
                    <div className="text-gray-500 mt-2">{option.period}</div>
                  </div>

                  <button className={`w-full ${option.popular ? 'btn-primary' : 'btn-secondary'} btn-large`}>
                    {option.cta}
                  </button>
                </div>

                {/* Features */}
                <div className="space-y-4">
                  {option.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center">
                      <div className="w-5 h-5 bg-success/20 rounded-full flex items-center justify-center mr-4">
                        <svg className="w-3 h-3 text-success" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Value Proposition */}
        <div className="text-center">
          <div className="bg-white rounded-3xl p-12 border border-gray-200 shadow-lg">
            <h3 className="heading-small font-bold text-primary mb-8">
              AI Governance Platform Advantage
            </h3>

            <div className="grid md:grid-cols-2 gap-12 mb-8">
              <div className="text-left">
                <h4 className="text-xl font-bold text-primary mb-6 flex items-center">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                  Traditional AI Management
                </h4>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-3"></span>
                    Data sent to external providers
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-3"></span>
                    No governance or control
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-3"></span>
                    Multiple separate integrations
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-3"></span>
                    Manual compliance processes
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-red-400 rounded-full mr-3"></span>
                    Limited cost visibility
                  </li>
                </ul>
              </div>

              <div className="text-left">
                <h4 className="text-xl font-bold text-primary mb-6 flex items-center">
                  <div className="w-8 h-8 bg-success/20 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-success" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  Difinity AI Governance Platform
                </h4>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                    Complete data sovereignty
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                    Comprehensive governance & control
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                    Unified API for 50+ models
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                    Automated compliance verification
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-success rounded-full mr-3"></span>
                    Real-time cost optimization
                  </li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary btn-large">
                Schedule Enterprise Demo
              </button>
              <button className="btn-secondary btn-large">
                Contact Sales
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}