export default function PlatformSection() {
  const platformFeatures = [
    {
      number: "01",
      title: "Risk Management Simplified",
      description: "Simplifies risk management with automated discovery, risk scoring, and lifecycle assessments. Identify, verify, mitigate, and monitor AI risks across technical and governance verticals with tailored actions and clear reports.",
      icon: "🛡️"
    },
    {
      number: "02", 
      title: "Action-Ready Data",
      description: "Turn insights into impact with centralized dashboards, detailed risk reports, and real-time monitoring. Get structured, exportable data to drive decisions, track compliance, and act fast across your AI ecosystem.",
      icon: "📊"
    },
    {
      number: "03",
      title: "Automated Oversight", 
      description: "Ensure continuous AI governance with automated alerts, signoffs, and monitoring. Deliver end-to-end oversight—from onboarding to deployment—flagging risks, enforcing controls, and keeping you audit-ready at all times.",
      icon: "🔄"
    },
    {
      number: "04",
      title: "Effortless Scalability",
      description: "Scale AI governance seamlessly with our modular platform. From intake to risk assessment and compliance, it adapts to enterprise needs—enabling consistent oversight across teams, geographies, and evolving AI portfolios.",
      icon: "📈"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container-width">
        <div className="text-center mb-16">
          <h2 className="text-sm font-semibold text-accent uppercase tracking-wide mb-4">
            Our Platform
          </h2>
          <h3 className="heading-medium font-bold text-primary mb-6">
            Risk Management Simplified
          </h3>
        </div>

        <div className="space-y-24">
          {platformFeatures.map((feature, index) => (
            <div
              key={index}
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Content */}
              <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                <div className="flex items-center mb-6">
                  <span className="text-4xl mr-4">{feature.icon}</span>
                  <span className="text-sm font-bold text-accent">{feature.number}</span>
                </div>
                <h4 className="heading-small font-bold text-primary mb-4">
                  {feature.title}
                </h4>
                <p className="text-lg text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>

              {/* Visual */}
              <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                <div className="bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl p-8 border border-gray-200">
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="h-4 bg-primary/20 rounded w-1/3"></div>
                        <div className="h-4 bg-accent/20 rounded w-1/4"></div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="h-20 bg-gray-100 rounded-lg"></div>
                        <div className="h-20 bg-gray-100 rounded-lg"></div>
                        <div className="h-20 bg-gray-100 rounded-lg"></div>
                      </div>
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
