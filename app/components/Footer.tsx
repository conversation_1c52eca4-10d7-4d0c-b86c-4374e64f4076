import Logo from "./Logo";
import SocialLinks from "./SocialLinks";

export default function Footer() {
  return (
    <footer className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white py-20 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

      <div className="container-width relative z-10">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-12">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <div className="w-40 mb-6">
              <Logo disableAnimation />
            </div>
            <p className="text-xl text-slate-300 mb-6 leading-relaxed">
              Enterprise AI infrastructure platform with complete security, governance, and cost transparency. Deploy anywhere with confidence.
            </p>
            <div className="flex items-center space-x-4">
              <div className="stat-card bg-slate-800 bg-opacity-50 border-slate-700">
                <div className="text-2xl font-bold gradient-text">SOC 2</div>
                <div className="text-xs text-slate-400">Compliant</div>
              </div>
              <div className="stat-card bg-slate-800 bg-opacity-50 border-slate-700">
                <div className="text-2xl font-bold gradient-text">50+</div>
                <div className="text-xs text-slate-400">AI Models</div>
              </div>
              <div className="stat-card bg-slate-800 bg-opacity-50 border-slate-700">
                <div className="text-2xl font-bold gradient-text">10%</div>
                <div className="text-xs text-slate-400">Transparent Markup</div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-6 gradient-text">Platform</h3>
            <ul className="space-y-4">
              <li><a href="#features" className="text-slate-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Features</a></li>
              <li><a href="#pricing" className="text-slate-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Pricing</a></li>
              <li><a href="#about" className="text-slate-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">About</a></li>
              <li><a href="#contact" className="text-slate-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Contact</a></li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-xl font-bold mb-6 gradient-text">Company</h3>
            <ul className="space-y-4">
              <li><a href="/privacy-policy" className="text-slate-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Privacy Policy</a></li>
              <li><a href="/terms" className="text-slate-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Terms of Service</a></li>
              <li><a href="/security" className="text-slate-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Security</a></li>
              <li><a href="/careers" className="text-slate-400 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Careers</a></li>
            </ul>

            <div className="mt-8">
              <h4 className="text-lg font-semibold mb-4 text-slate-300">Contact</h4>
              <div className="space-y-3">
                <div className="flex items-center text-slate-400">
                  <i className="fas fa-envelope mr-3 text-blue-400"></i>
                  <EMAIL>
                </div>
                <div className="flex items-center text-slate-400">
                  <i className="fas fa-map-marker-alt mr-3 text-purple-400"></i>
                  Sydney, NSW
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-16 pt-8 border-t border-slate-700 flex flex-col md:flex-row justify-between items-center">
          <div className="text-slate-400 text-sm mb-4 md:mb-0">
            © {new Date().getFullYear()} Difinity.ai. All rights reserved.
          </div>

          {/* Social Links */}
          <div className="flex items-center space-x-6">
            <SocialLinks />
          </div>
        </div>
      </div>
    </footer>
  );
}