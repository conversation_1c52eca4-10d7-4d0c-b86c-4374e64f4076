export default function BenefitsSection() {
  const benefits = [
    {
      icon: "🛡️",
      title: "Enhanced Privacy & Control",
      description: "Complete data sovereignty with all processing within your infrastructure",
      metrics: "100% Data Retention",
      color: "accent"
    },
    {
      icon: "💰",
      title: "Cost Optimization",
      description: "Intelligent routing and monitoring reduce AI costs significantly",
      metrics: "20-40% Cost Reduction",
      color: "success"
    },
    {
      icon: "⚖️",
      title: "Simplified Compliance",
      description: "Automated compliance checking for multiple regulatory frameworks",
      metrics: "EU AI Act Ready",
      color: "primary"
    },
    {
      icon: "⚡",
      title: "Operational Efficiency",
      description: "Unified API reduces complexity and management overhead",
      metrics: "50+ Models, 1 API",
      color: "accent"
    },
    {
      icon: "📈",
      title: "Scalability & Flexibility",
      description: "Infrastructure-agnostic design grows with your needs",
      metrics: "Any Infrastructure",
      color: "success"
    },
    {
      icon: "🔒",
      title: "Enterprise Security",
      description: "Zero-trust architecture with end-to-end encryption",
      metrics: "SOC 2 Compliant",
      color: "primary"
    }
  ];

  const comparisonData = [
    {
      feature: "Data Location",
      traditional: "External providers",
      difinity: "Your infrastructure",
      advantage: true
    },
    {
      feature: "Cost Transparency",
      traditional: "Hidden fees",
      difinity: "Real-time monitoring",
      advantage: true
    },
    {
      feature: "Compliance",
      traditional: "Manual processes",
      difinity: "Automated verification",
      advantage: true
    },
    {
      feature: "Model Management",
      traditional: "Multiple integrations",
      difinity: "Unified API",
      advantage: true
    },
    {
      feature: "Deployment",
      traditional: "Cloud-only",
      difinity: "Anywhere",
      advantage: true
    }
  ];

  return (
    <section className="py-24 bg-secondary-light">
      <div className="container-width">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-success/10 border border-success/20 mb-6">
            <span className="text-sm font-medium text-success">
              ✨ Benefits & Value
            </span>
          </div>
          <h2 className="heading-medium font-bold text-primary mb-6">
            Transform Your AI Operations
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Difinity AI delivers measurable benefits across security, cost, compliance, 
            and operational efficiency for enterprise AI workloads.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white rounded-2xl p-8 border border-gray-100 hover:shadow-lg transition-shadow duration-300">
              <div className={`w-16 h-16 rounded-2xl flex items-center justify-center mb-6 ${
                benefit.color === 'accent' ? 'bg-accent/10' :
                benefit.color === 'success' ? 'bg-success/10' : 'bg-primary/10'
              }`}>
                <span className="text-2xl">{benefit.icon}</span>
              </div>
              
              <h3 className="text-xl font-bold text-primary mb-3">{benefit.title}</h3>
              <p className="text-gray-600 mb-4 leading-relaxed">{benefit.description}</p>
              
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                benefit.color === 'accent' ? 'bg-accent/10 text-accent' :
                benefit.color === 'success' ? 'bg-success/10 text-success' : 'bg-primary/10 text-primary'
              }`}>
                {benefit.metrics}
              </div>
            </div>
          ))}
        </div>

        {/* Comparison Table */}
        <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg">
          <h3 className="text-2xl font-bold text-primary text-center mb-8">
            Traditional AI vs. Difinity AI
          </h3>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-4 px-6 font-semibold text-primary">Feature</th>
                  <th className="text-center py-4 px-6 font-semibold text-gray-600">Traditional AI</th>
                  <th className="text-center py-4 px-6 font-semibold text-accent">Difinity AI</th>
                </tr>
              </thead>
              <tbody>
                {comparisonData.map((row, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-6 font-medium text-primary">{row.feature}</td>
                    <td className="py-4 px-6 text-center">
                      <div className="flex items-center justify-center">
                        <span className="text-red-500 mr-2">❌</span>
                        <span className="text-gray-600">{row.traditional}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-center">
                      <div className="flex items-center justify-center">
                        <span className="text-success mr-2">✅</span>
                        <span className="text-accent font-medium">{row.difinity}</span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* ROI Calculator CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-success/10 to-accent/10 rounded-2xl p-8 border border-success/20">
            <h3 className="text-2xl font-bold text-primary mb-4">
              Calculate Your ROI
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              See how much you can save with Difinity AI's cost optimization and efficiency improvements.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary btn-large">
                ROI Calculator
              </button>
              <button className="btn-secondary btn-large">
                Case Studies
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
