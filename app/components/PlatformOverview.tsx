export default function PlatformOverview() {
  const challenges = [
    {
      icon: "🔒",
      title: "Data Privacy Concerns",
      description: "Traditional AI solutions require sending sensitive data to external providers"
    },
    {
      icon: "💰",
      title: "Unpredictable Costs",
      description: "Hidden fees and surprise bills make AI cost management impossible"
    },
    {
      icon: "⚖️",
      title: "Compliance Challenges",
      description: "Meeting regulatory requirements across different jurisdictions"
    },
    {
      icon: "🔧",
      title: "Management Overhead",
      description: "Each AI model requires separate integrations and management"
    }
  ];

  const solutions = [
    {
      icon: "🛡️",
      title: "Complete Data Sovereignty",
      description: "Your data never leaves your infrastructure. Process AI workloads locally with full control."
    },
    {
      icon: "📊",
      title: "Transparent Cost Control",
      description: "Real-time cost monitoring, optimization recommendations, and predictable pricing."
    },
    {
      icon: "✅",
      title: "Automated Compliance",
      description: "Built-in compliance frameworks for EU AI Act, GDPR, CCPA, and more."
    },
    {
      icon: "🔄",
      title: "Unified Management",
      description: "Single API for 50+ AI models with intelligent routing and governance."
    }
  ];

  return (
    <section className="py-24 bg-secondary-light">
      <div className="container-width">
        {/* Section Header */}
        <div className="text-center mb-20">
          <h2 className="heading-medium font-bold text-primary mb-6">
            The Enterprise AI Challenge
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Organizations struggle with AI governance, data privacy, cost control, and compliance. 
            Difinity solves these challenges with a unified platform that keeps your data secure.
          </p>
        </div>

        {/* Challenges */}
        <div className="mb-20">
          <h3 className="text-2xl font-bold text-primary text-center mb-12">
            Current Challenges
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {challenges.map((challenge, index) => (
              <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 text-center">
                <div className="text-4xl mb-4">{challenge.icon}</div>
                <h4 className="font-bold text-primary mb-3">{challenge.title}</h4>
                <p className="text-gray-600 text-sm">{challenge.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Arrow Divider */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-accent rounded-full">
            <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        </div>

        {/* Solutions */}
        <div>
          <h3 className="text-2xl font-bold text-primary text-center mb-12">
            Difinity AI Solutions
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {solutions.map((solution, index) => (
              <div key={index} className="bg-white rounded-xl p-6 border border-accent/20 hover:border-accent/50 transition-colors duration-200 text-center">
                <div className="text-4xl mb-4">{solution.icon}</div>
                <h4 className="font-bold text-primary mb-3">{solution.title}</h4>
                <p className="text-gray-600 text-sm">{solution.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-accent/10 to-primary/10 rounded-2xl p-8 border border-accent/20">
            <h3 className="text-2xl font-bold text-primary mb-4">
              Ready to Transform Your AI Governance?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join enterprises that have taken control of their AI workloads with complete data sovereignty and governance.
            </p>
            <button className="btn-primary btn-large">
              Schedule a Demo
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
