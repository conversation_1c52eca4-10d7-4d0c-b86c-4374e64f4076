export default function AboutSection() {
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "CEO & Founder",
      image: "/team/wasim.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON>",
      role: "COO & Co-founder",
      image: "/team/atiq.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "CTO & Co-founder",
      image: "/team/shoaib.jpg",
      description: ""
    },
    // ... other team members
  ];

  return (
    <div className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-purple-50/30"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

      <div className="container-width relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-slate-100 to-amber-50 border border-slate-200/50 mb-6">
            <span className="text-sm font-semibold bg-gradient-to-r from-slate-700 to-amber-600 bg-clip-text text-transparent">🏢 About Difinity.ai</span>
          </div>
          <h2 className="section-heading mb-6">
            Enterprise AI <span className="gradient-text-accent">Infrastructure</span>
          </h2>
          <p className="section-subheading max-w-3xl mx-auto">
            Leading the future of enterprise AI infrastructure with security, governance, and cost transparency at the core.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 mb-20">
          <div className="space-y-8" data-aos="fade-right">
            <div>
              <h3 className="text-3xl font-bold text-slate-800 mb-4">Our Mission</h3>
              <p className="text-xl text-slate-600 leading-relaxed mb-6">
                To democratize AI infrastructure by giving organizations complete control, privacy, and cost transparency over their AI workloads.
              </p>
              <p className="text-slate-600 leading-relaxed">
                We're building the future where AI deployment is infrastructure-agnostic, privacy-first, and cost-intelligent.
                Your data stays where it belongs - with you.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="stat-card">
                <div className="text-4xl font-bold gradient-text mb-2">100%</div>
                <div className="text-slate-600">Data Privacy</div>
              </div>
              <div className="stat-card">
                <div className="text-4xl font-bold gradient-text mb-2">50+</div>
                <div className="text-slate-600">AI Models</div>
              </div>
              <div className="stat-card">
                <div className="text-4xl font-bold gradient-text mb-2">99.9%</div>
                <div className="text-slate-600">Uptime SLA</div>
              </div>
              <div className="stat-card">
                <div className="text-4xl font-bold gradient-text mb-2">60%</div>
                <div className="text-slate-600">Cost Savings</div>
              </div>
            </div>
          </div>

          <div className="space-y-8" data-aos="fade-left">
            <div className="feature-highlight">
              <h3 className="text-2xl font-bold text-slate-800 mb-6">Our Core Values</h3>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="icon-container mr-4 flex-shrink-0">
                    <i className="fas fa-shield-alt text-teal-600"></i>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-slate-800 mb-2">Privacy First</h4>
                    <p className="text-slate-600">Your data never leaves your infrastructure. Complete sovereignty and control.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="icon-container mr-4 flex-shrink-0">
                    <i className="fas fa-chart-line text-orange-600"></i>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-slate-800 mb-2">Cost Transparency</h4>
                    <p className="text-slate-600">Real-time monitoring and optimization of AI costs with complete visibility.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="icon-container mr-4 flex-shrink-0">
                    <i className="fas fa-globe text-cyan-600"></i>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-slate-800 mb-2">Infrastructure Agnostic</h4>
                    <p className="text-slate-600">Deploy anywhere - on-premises, hybrid, or multi-cloud environments.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="text-center mb-16" data-aos="fade-up">
          <h3 className="text-3xl font-bold mb-4 gradient-text-accent">Meet Our Team</h3>
          <p className="text-xl text-slate-600">Passionate experts building the future of AI infrastructure</p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-20">
          {teamMembers.map((member, index) => (
            <div key={index} className="card-feature text-center group" data-aos="fade-up" data-aos-delay={index * 150}>
              <div className="w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-br from-teal-100 to-orange-100 flex items-center justify-center">
                <div className="w-24 h-24 rounded-full flex items-center justify-center text-white text-2xl font-bold" style={{background: 'var(--primary-gradient)'}}>
                  {member.name.split(' ').map(n => n[0]).join('')}
                </div>
              </div>
              <h4 className="text-xl font-bold text-slate-800 mb-2">{member.name}</h4>
              <p className="gradient-text font-semibold mb-4">{member.role}</p>
              <p className="text-slate-600">{member.description || "Building the future of AI infrastructure with passion and expertise."}</p>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center" data-aos="fade-up">
          <div className="rounded-3xl p-12 border border-blue-200" style={{background: 'linear-gradient(to right, rgba(37, 99, 235, 0.1), rgba(147, 51, 234, 0.1))'}}>
            <h3 className="text-3xl font-bold mb-4 gradient-text-accent">Ready to Transform Your AI Infrastructure?</h3>
            <p className="text-xl text-slate-600 mb-8 max-w-2xl mx-auto">
              Join forward-thinking organizations that prioritize data privacy, cost control, and deployment flexibility.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                <span>Start Free Trial</span>
              </button>
              <button className="btn-accent">
                <span>Contact Our Team</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}