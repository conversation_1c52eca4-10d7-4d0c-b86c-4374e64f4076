export default function DeploymentOptions() {
  const deploymentOptions = [
    {
      title: "On-Premises",
      subtitle: "Maximum Security & Control",
      description: "Deploy Difinity AI within your own data centers for complete air-gapped security and data sovereignty.",
      icon: "🏢",
      features: [
        "Complete data isolation",
        "Air-gapped security",
        "Custom security policies",
        "Regulatory compliance",
        "No internet dependency",
        "Full infrastructure control"
      ],
      bestFor: "Highly regulated industries, government, financial services",
      security: "Maximum",
      control: "Complete",
      complexity: "High"
    },
    {
      title: "Hybrid Cloud",
      subtitle: "Balanced Flexibility",
      description: "Combine on-premises security for sensitive workloads with cloud scalability for general AI tasks.",
      icon: "🌐",
      features: [
        "Workload distribution",
        "Data locality control",
        "Scalable resources",
        "Cost optimization",
        "Disaster recovery",
        "Flexible policies"
      ],
      bestFor: "Large enterprises with mixed workload requirements",
      security: "High",
      control: "Flexible",
      complexity: "Medium"
    },
    {
      title: "Multi-Cloud",
      subtitle: "Vendor Independence",
      description: "Deploy across multiple cloud providers to avoid vendor lock-in and optimize for performance and cost.",
      icon: "☁️",
      features: [
        "Vendor independence",
        "Global deployment",
        "Cost optimization",
        "Performance tuning",
        "Risk distribution",
        "Compliance zones"
      ],
      bestFor: "Global enterprises requiring geographic distribution",
      security: "High",
      control: "Distributed",
      complexity: "Medium"
    },
    {
      title: "Edge Computing",
      subtitle: "Ultra-Low Latency",
      description: "Deploy at the edge for minimal latency and local data processing requirements.",
      icon: "⚡",
      features: [
        "Ultra-low latency",
        "Local processing",
        "Bandwidth optimization",
        "Offline capability",
        "Real-time responses",
        "Edge intelligence"
      ],
      bestFor: "IoT, manufacturing, real-time applications",
      security: "Distributed",
      control: "Local",
      complexity: "Low"
    }
  ];

  const securityFeatures = [
    {
      title: "Zero Trust Architecture",
      description: "No implicit trust assumptions with continuous verification",
      icon: "🔐"
    },
    {
      title: "End-to-End Encryption",
      description: "All data encrypted in transit and at rest",
      icon: "🔒"
    },
    {
      title: "Key Management",
      description: "Support for organizational key management systems",
      icon: "🗝️"
    },
    {
      title: "Audit Trails",
      description: "Comprehensive logging for all activities and access",
      icon: "📋"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container-width">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 border border-primary/20 mb-6">
            <span className="text-sm font-medium text-primary">
              🚀 Deployment Options
            </span>
          </div>
          <h2 className="heading-medium font-bold text-primary mb-6">
            Deploy Anywhere, Control Everything
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Infrastructure-agnostic design allows deployment in any environment while 
            maintaining complete data sovereignty and governance capabilities.
          </p>
        </div>

        {/* Deployment Options Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-20">
          {deploymentOptions.map((option, index) => (
            <div key={index} className="bg-white rounded-2xl p-8 border-2 border-gray-100 hover:border-accent/30 hover:shadow-xl transition-all duration-300">
              {/* Header */}
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-accent/10 rounded-2xl flex items-center justify-center mr-4">
                  <span className="text-3xl">{option.icon}</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-primary">{option.title}</h3>
                  <p className="text-accent font-medium">{option.subtitle}</p>
                </div>
              </div>

              <p className="text-gray-600 mb-6 leading-relaxed">{option.description}</p>

              {/* Features */}
              <div className="mb-6">
                <h4 className="font-semibold text-primary mb-3">Key Features:</h4>
                <div className="grid grid-cols-2 gap-2">
                  {option.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center text-sm">
                      <div className="w-4 h-4 bg-success/20 rounded-full flex items-center justify-center mr-2">
                        <svg className="w-2 h-2 text-success" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Characteristics */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-xs text-gray-500 mb-1">Security</div>
                  <div className="text-sm font-medium text-primary">{option.security}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-500 mb-1">Control</div>
                  <div className="text-sm font-medium text-primary">{option.control}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-500 mb-1">Complexity</div>
                  <div className="text-sm font-medium text-primary">{option.complexity}</div>
                </div>
              </div>

              {/* Best For */}
              <div className="bg-accent/5 rounded-lg p-4">
                <div className="text-xs text-accent font-medium mb-1">BEST FOR</div>
                <div className="text-sm text-gray-700">{option.bestFor}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Security Features */}
        <div className="bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl p-8 border border-gray-200 mb-16">
          <h3 className="text-2xl font-bold text-primary text-center mb-8">
            Enterprise Security Across All Deployments
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {securityFeatures.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm">
                  <span className="text-xl">{feature.icon}</span>
                </div>
                <h4 className="font-semibold text-primary mb-2">{feature.title}</h4>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg">
            <h3 className="text-2xl font-bold text-primary mb-4">
              Need Help Choosing the Right Deployment?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Our solution architects can help you design the optimal deployment strategy 
              for your specific requirements and constraints.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary btn-large">
                Consult Solution Architect
              </button>
              <button className="btn-secondary btn-large">
                Deployment Guide
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
