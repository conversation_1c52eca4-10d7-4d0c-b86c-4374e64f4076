export default function HeroSection() {
  return (
    <section className="relative bg-white pt-20 pb-32 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-30"></div>
      
      <div className="container-width relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-accent/10 border border-accent/20 mb-8">
            <span className="text-sm font-medium text-accent">
              🚀 Enterprise AI Infrastructure Platform
            </span>
          </div>

          {/* Main Heading */}
          <h1 className="heading-large font-bold text-primary mb-6 leading-tight">
            Accelerate AI Transformation
          </h1>
          
          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
            Drive innovation, mitigate risks, and scale AI from concept to production faster with the industry-leading AI governance platform
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-center mb-16">
            <button className="btn-primary btn-large">
              Get a demo
            </button>
            <button className="btn-secondary btn-large">
              Learn more
            </button>
          </div>

          {/* Hero Image Placeholder */}
          <div className="relative max-w-5xl mx-auto">
            <div className="bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl p-8 border border-gray-200">
              <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div className="h-3 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="space-y-3">
                    <div className="h-3 bg-accent/20 rounded"></div>
                    <div className="h-3 bg-accent/20 rounded w-3/4"></div>
                    <div className="h-3 bg-accent/20 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
