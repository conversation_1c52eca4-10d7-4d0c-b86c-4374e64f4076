export default function HeroSection() {
  return (
    <section className="relative bg-white pt-20 pb-32 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

      <div className="container-width relative z-10">
        <div className="text-center max-w-5xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-accent/10 border border-accent/20 mb-8">
            <span className="text-sm font-medium text-accent">
              🛡️ Enterprise AI Governance Platform
            </span>
          </div>

          {/* Main Heading */}
          <h1 className="heading-large font-bold text-primary mb-6 leading-tight">
            Complete AI Governance.<br />
            <span className="gradient-text">Your Data Never Leaves.</span>
          </h1>

          <p className="text-xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
            Unified platform for managing all AI workloads with complete data sovereignty,
            comprehensive governance, and cost optimization. Deploy within your infrastructure
            while maintaining full control and compliance.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-center mb-16">
            <button className="btn-primary btn-large">
              Schedule Demo
            </button>
            <button className="btn-secondary btn-large">
              View Platform
            </button>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto mb-16">
            <div className="text-center">
              <div className="text-3xl font-bold text-accent mb-2">100%</div>
              <div className="text-sm text-gray-600">Data Sovereignty</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent mb-2">50+</div>
              <div className="text-sm text-gray-600">AI Models</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent mb-2">99.9%</div>
              <div className="text-sm text-gray-600">Uptime SLA</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent mb-2">40%</div>
              <div className="text-sm text-gray-600">Cost Reduction</div>
            </div>
          </div>

          {/* Platform Preview */}
          <div className="relative max-w-6xl mx-auto">
            <div className="bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl p-8 border border-gray-200">
              <div className="bg-white rounded-xl shadow-2xl border border-gray-100 overflow-hidden">
                {/* Mock Dashboard Header */}
                <div className="bg-primary text-white p-4 flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">D</span>
                    </div>
                    <span className="font-semibold">Difinity Hub</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-success rounded-full"></div>
                    <span className="text-sm">All Systems Operational</span>
                  </div>
                </div>

                {/* Mock Dashboard Content */}
                <div className="p-6">
                  <div className="grid grid-cols-3 gap-6 mb-6">
                    <div className="bg-accent/10 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-accent mb-1">1,247</div>
                      <div className="text-sm text-gray-600">API Calls Today</div>
                    </div>
                    <div className="bg-success/10 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-success mb-1">$127.50</div>
                      <div className="text-sm text-gray-600">Cost This Month</div>
                    </div>
                    <div className="bg-primary/10 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-primary mb-1">12</div>
                      <div className="text-sm text-gray-600">Active Models</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <div className="h-4 bg-gray-200 rounded mb-3"></div>
                      <div className="space-y-2">
                        <div className="h-3 bg-accent/20 rounded"></div>
                        <div className="h-3 bg-accent/20 rounded w-3/4"></div>
                        <div className="h-3 bg-accent/20 rounded w-1/2"></div>
                      </div>
                    </div>
                    <div>
                      <div className="h-4 bg-gray-200 rounded mb-3"></div>
                      <div className="space-y-2">
                        <div className="h-3 bg-success/20 rounded"></div>
                        <div className="h-3 bg-success/20 rounded w-2/3"></div>
                        <div className="h-3 bg-success/20 rounded w-3/4"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
