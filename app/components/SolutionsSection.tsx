export default function SolutionsSection() {
  const solutions = [
    {
      category: "By Regulation",
      items: [
        { name: "EU AI Act", description: "Comprehensive compliance framework" },
        { name: "NIST AI RMF", description: "Risk management framework" },
        { name: "SOC 2 Compliance", description: "Security and availability controls" },
        { name: "ISO/IEC 42001", description: "AI management system certification" }
      ]
    },
    {
      category: "By Industry", 
      items: [
        { name: "Financial Services", description: "Banking and fintech solutions" },
        { name: "Healthcare", description: "Medical AI governance" },
        { name: "Manufacturing", description: "Industrial AI oversight" },
        { name: "Technology", description: "Software and platform governance" }
      ]
    }
  ];

  const partners = [
    {
      category: "Consulting & Delivery",
      logos: ["🏢", "⚖️"]
    },
    {
      category: "Policy, Standards and Engagement", 
      logos: ["🏛️", "📋", "🌐", "📊", "🔬", "📈"]
    },
    {
      category: "Technology",
      logos: ["☁️", "🔧", "💻", "🔍", "🖥️", "🚀"]
    }
  ];

  return (
    <section className="py-24 bg-secondary-light">
      <div className="container-width">
        {/* Solutions Grid */}
        <div className="grid lg:grid-cols-2 gap-16 mb-24">
          {solutions.map((solution, index) => (
            <div key={index} className="card-feature">
              <h3 className="text-xl font-bold text-primary mb-6">
                {solution.category}
              </h3>
              <div className="space-y-4">
                {solution.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="border-l-4 border-accent pl-4">
                    <h4 className="font-semibold text-primary mb-1">
                      {item.name}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {item.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Partners Section */}
        <div className="text-center mb-16">
          <h3 className="heading-medium font-bold text-primary mb-12">
            Our Partners
          </h3>
        </div>

        <div className="space-y-12">
          {partners.map((partner, index) => (
            <div key={index} className="text-center">
              <h4 className="text-lg font-semibold text-primary mb-6">
                {partner.category}
              </h4>
              <div className="flex flex-wrap justify-center items-center gap-8">
                {partner.logos.map((logo, logoIndex) => (
                  <div
                    key={logoIndex}
                    className="w-16 h-16 bg-white rounded-lg shadow-sm border border-gray-200 flex items-center justify-center text-2xl hover:shadow-md transition-shadow duration-200"
                  >
                    {logo}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
