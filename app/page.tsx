import Navigation from './components/Navigation';
import Footer from './components/Footer';
import FeaturesSection from './components/FeaturesSection';
import PricingSection from './components/PricingSection';
import AboutSection from './components/AboutSection';
import ContactSection from './components/ContactSection';

export default function Home() {
  return (
    <main className="min-h-screen overflow-x-hidden">
      {/* Hero Section */}
      <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Parallax Background Layers */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-teal-50/30 to-orange-50/20"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

        {/* Floating Elements with new colors */}
        <div className="absolute top-20 left-10 w-24 h-24 bg-gradient-to-br from-teal-400/20 to-cyan-400/20 rounded-full animate-float"></div>
        <div className="absolute top-40 right-20 w-20 h-20 bg-gradient-to-br from-orange-400/20 to-amber-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-40 left-20 w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-teal-400/20 rounded-full animate-float" style={{animationDelay: '4s'}}></div>
        <div className="absolute top-1/2 right-1/3 w-12 h-12 bg-gradient-to-br from-orange-300/15 to-red-300/15 rounded-full animate-float" style={{animationDelay: '6s'}}></div>

        <div className="container-width relative z-10">
          <div className="text-center max-w-5xl mx-auto">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 border border-blue-200/50 mb-8" data-aos="fade-down">
              <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                🚀 Privacy-First AI Infrastructure for Enterprise
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="section-heading mb-8" data-aos="fade-up" data-aos-delay="200">
              <span className="block">Your Data.</span>
              <span className="block">Your Control.</span>
              <span className="block gradient-text">Your AI Infrastructure.</span>
            </h1>

            {/* Subheading */}
            <p className="section-subheading mb-12 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="400">
              Deploy, monitor, and control AI workloads anywhere—on-premises, hybrid, or multi-cloud.
              Keep your data private while optimizing costs and maintaining complete oversight.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 items-center justify-center mb-16" data-aos="fade-up" data-aos-delay="600">
              <button className="btn-primary group">
                <span>Start Free Trial</span>
                <i className="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
              </button>
              <button className="btn-secondary group">
                <span>Watch Demo</span>
                <i className="fas fa-play ml-2 group-hover:scale-110 transition-transform duration-300"></i>
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="800">
              <div className="stat-card">
                <div className="text-3xl font-bold gradient-text mb-2">100%</div>
                <div className="text-sm text-slate-600">Data Privacy</div>
              </div>
              <div className="stat-card">
                <div className="text-3xl font-bold gradient-text mb-2">50+</div>
                <div className="text-sm text-slate-600">AI Models</div>
              </div>
              <div className="stat-card">
                <div className="text-3xl font-bold gradient-text mb-2">99.9%</div>
                <div className="text-sm text-slate-600">Uptime SLA</div>
              </div>
              <div className="stat-card">
                <div className="text-3xl font-bold gradient-text mb-2">60%</div>
                <div className="text-sm text-slate-600">Cost Savings</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-slate-300 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features">
        <FeaturesSection />
      </section>

      {/* Pricing Section */}
      <section id="pricing">
        <PricingSection />
      </section>

      {/* About Section */}
      <section id="about">
        <AboutSection />
      </section>

      {/* Contact Section */}
      <section id="contact">
        <ContactSection />
      </section>

      {/* Footer */}
      <Footer />
    </main>
  );
}